import logging
import datetime
from fastapi import FastAPI
from contextlib import asynccontextmanager

# Import core components
from app.core.config import settings
from app.core.database import init_supabase, close_supabase_client, init_db_pools, close_db_pools

# Import API routers
from app.api.v1 import auth as auth_router_v1
from app.api.v1 import agents as agents_router_v1
from app.api.v1 import chat as chat_router_v1
from app.api.v1 import threads as threads_router_v1
from app.api.v1 import tools as tools_router_v1
from app.api.v1 import doc_exports as doc_exports_router_v1 # Added import
from app.api.v1 import crm_empresas as crm_empresas_router_v1 # CRM Empresas import
from app.api.v1 import crm_personas as crm_personas_router_v1 # CRM Personas import

from app.api.v1 import reuniones as reuniones_router_v1 # Reuniones import
from app.api.v1 import proyectos as proyectos_router_v1 # Proyectos import
from app.api.v1 import procesos as procesos_router_v1 # Procesos import
from app.api.v1 import tareas as tareas_router_v1 # Tareas import
from app.api.v1 import dashboard as dashboard_router_v1 # Dashboard import
from app.api.v1 import search as search_router_v1 # Search import
from app.api.v1 import procesos_clientes as procesos_clientes_router_v1 # Procesos Clientes import
from app.api.v1 import ideas as ideas_router_v1 # Ideas import
from app.api.v1 import usuarios as usuarios_router_v1 # Usuarios import

# Configure logging
# Use environment variable for log level, default to INFO for production safety
import os
log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
logging.basicConfig(level=getattr(logging, log_level, logging.INFO), format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__) # Get logger for main module


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan context manager.
    Handles startup and shutdown events.
    """
    logger.info("Application startup...")
    # Initialize Supabase client on startup
    await init_supabase()
    # Initialize AsyncPG database pools
    await init_db_pools()
    yield
    # Close database pools and Supabase client on shutdown
    logger.info("Application shutdown...")
    await close_db_pools()
    await close_supabase_client()

# Initialize FastAPI app with lifespan manager
app = FastAPI(
    title="Aceleralia Internal App API",
    description="API for the internal Aceleralia application.",
    version="0.1.0",
    lifespan=lifespan
)

# --- API Routers ---
# Include routers
logger.info("Registering API routers...")

logger.info("Registering auth router...")
app.include_router(auth_router_v1.router, prefix="/api/v1/auth", tags=["Auth V1"])

logger.info("Registering agents router...")
app.include_router(agents_router_v1.router, prefix="/api/v1/agents", tags=["Agents V1"])

logger.info("Registering chat router...")
app.include_router(chat_router_v1.router, prefix="/api/v1/chat", tags=["Chat V1"])

logger.info("Registering threads router...")
app.include_router(threads_router_v1.router, prefix="/api/v1/threads", tags=["Threads V1"])

logger.info("Registering tools router...")
app.include_router(tools_router_v1.router, prefix="/api/v1/tools", tags=["Tools V1"])

logger.info("Registering doc_exports router...")
app.include_router(doc_exports_router_v1.router, prefix="/api/v1/doc_exports", tags=["Doc Exports V1"])

logger.info("Registering empresas router...")
app.include_router(crm_empresas_router_v1.router, prefix="/api/v1/empresas", tags=["CRM Empresas V1"])

logger.info("Registering personas router...")
app.include_router(crm_personas_router_v1.router, prefix="/api/v1/personas", tags=["CRM Personas V1"])



logger.info("Registering reuniones router...")
# Check if reuniones router has routes before registering
if hasattr(reuniones_router_v1.router, 'routes'):
    logger.info(f"Reuniones router imported successfully. Router has {len(reuniones_router_v1.router.routes)} routes.")
    for route in reuniones_router_v1.router.routes:
        logger.info(f"  Route: {route.methods} {route.path}")
else:
    logger.warning("Reuniones router does not have routes attribute!")

app.include_router(reuniones_router_v1.router, prefix="/api/v1/reuniones", tags=["Reuniones V1"])

logger.info("Registering proyectos router...")
app.include_router(proyectos_router_v1.router, prefix="/api/v1/proyectos", tags=["Proyectos V1"])

logger.info("Registering procesos router...")
app.include_router(procesos_router_v1.router, prefix="/api/v1/procesos", tags=["Procesos V1"])

logger.info("Registering tareas router...")
app.include_router(tareas_router_v1.router, prefix="/api/v1/tareas", tags=["Tareas V1"])

logger.info("Registering dashboard router...")
app.include_router(dashboard_router_v1.router, prefix="/api/v1/dashboard", tags=["Dashboard V1"])

logger.info("Registering search router...")
app.include_router(search_router_v1.router, prefix="/api/v1/search", tags=["Search V1"])

logger.info("Registering procesos_clientes router...")
app.include_router(procesos_clientes_router_v1.router, prefix="/api/v1", tags=["Procesos Clientes V1"])

logger.info("Registering ideas router...")
app.include_router(ideas_router_v1.router, prefix="/api/v1/ideas", tags=["Ideas V1"])

logger.info("Registering usuarios router...")
app.include_router(usuarios_router_v1.router, prefix="/api/v1/usuarios", tags=["Usuarios V1"])

logger.info("All routers registered successfully.")


# --- Root Endpoint ---
@app.get("/", tags=["Root"])
async def read_root():
    """
    Root endpoint providing basic application info.
    """
    return {"message": f"Welcome to {app.title} v{app.version}"}

# --- Health Check Endpoint ---
@app.get("/health", tags=["Health"])
async def health_check():
    """
    Simple health check endpoint.
    """
    logger.info("Health check endpoint called")
    # Add more checks if needed (e.g., database connectivity)
    return {"status": "ok", "timestamp": datetime.datetime.now().isoformat()}

# --- Debug Endpoint ---
@app.get("/debug/routes", tags=["Debug"])
async def debug_routes():
    """
    Debug endpoint to list all registered routes.
    """
    routes_info = []
    for route in app.routes:
        if hasattr(route, 'methods') and hasattr(route, 'path'):
            routes_info.append({
                "path": route.path,
                "methods": list(route.methods) if route.methods else [],
                "name": getattr(route, 'name', 'Unknown')
            })
    return {
        "total_routes": len(routes_info),
        "routes": routes_info
    }

# --- Request Logging Middleware ---
from fastapi import Request
from fastapi.responses import Response
import time

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    logger.info(f"🔍 Incoming request: {request.method} {request.url}")

    # Log headers but mask sensitive ones
    safe_headers = {}
    for key, value in request.headers.items():
        if key.lower() in ['authorization', 'x-api-key', 'cookie']:
            safe_headers[key] = "***MASKED***"
        else:
            safe_headers[key] = value
    logger.debug(f"🔍 Headers: {safe_headers}")  # Only log headers in DEBUG mode

    response = await call_next(request)

    process_time = time.time() - start_time
    logger.info(f"🔍 Response: {response.status_code} for {request.method} {request.url} in {process_time:.4f}s")

    return response

# --- CORS Middleware ---
# Allows frontend (on different origin) to communicate with backend
from fastapi.middleware.cors import CORSMiddleware
origins = [
    "http://localhost:5173", # Vite default port
    "http://localhost:5174", # Alternative Vite port
    "http://localhost:5175", # Another alternative Vite port
    "http://127.0.0.1:5173", # Also allow 127.0.0.1
    "http://127.0.0.1:5174", # Alternative 127.0.0.1 port
    "http://127.0.0.1:5175", # Another alternative 127.0.0.1 port
    "https://aplicacion.aceleralia.com", # Production Frontend
    "https://automatizaciones.aceleralia.com", # n8n instance
    # Add production frontend URL here later
]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins, # List of allowed origins
    allow_credentials=True, # Allow cookies
    allow_methods=["*"], # Allow all methods (GET, POST, etc.)
    allow_headers=["*"], # Allow all headers
)

if __name__ == "__main__":
    import uvicorn
    # Note: Run with `uvicorn backend.main:app --reload` for development
    # This block is mainly for debugging purposes if run directly
    logger.warning("Running in debug mode. Use 'uvicorn backend.main:app --reload' for development.")
    uvicorn.run(app, host="0.0.0.0", port=8000)