import logging
import re
from typing import <PERSON><PERSON>, Optional, Any
from fastapi import HTT<PERSON>Ex<PERSON>, status
from supabase import Client as SupabaseClient

from ..core.database import get_supabase_client
from ..models.tool import TextModificationRequest, TextModificationResponse, InstruccionCambio

logger = logging.getLogger(__name__)

class TextModificationService:
    """Service for handling text field modifications in database records."""
    
    def __init__(self):
        self.supabase: Optional[SupabaseClient] = None
    
    def _get_client(self) -> SupabaseClient:
        """Get Supabase client instance."""
        if not self.supabase:
            self.supabase = get_supabase_client()
        return self.supabase
    
    async def validate_table_and_column(self, tabla: str, columna: str) -> bool:
        """
        Validate that the specified table and column exist in the database.
        
        Args:
            tabla: Table name
            columna: Column name
            
        Returns:
            True if table and column exist
            
        Raises:
            HTTPException: If table or column doesn't exist
        """
        supabase = await self._get_client()
        
        try:
            # Check if table exists by trying to query its structure
            # This is a simple approach - we try to select from the table with a limit of 0
            response = supabase.table(tabla).select(columna).limit(0).execute()
            return True
        except Exception as e:
            logger.error(f"Table/column validation failed for {tabla}.{columna}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Table '{tabla}' or column '{columna}' does not exist or is not accessible."
            )
    
    async def get_record_content(self, tabla: str, columna: str, id_fila: str) -> str:
        """
        Get the current content of the specified field.
        
        Args:
            tabla: Table name
            columna: Column name
            id_fila: Record ID (UUID)
            
        Returns:
            Current content of the field (empty string if NULL)
            
        Raises:
            HTTPException: If record doesn't exist
        """
        supabase = await self._get_client()
        
        try:
            response = supabase.table(tabla).select(columna).eq('id', id_fila).maybe_single().execute()
            
            if not response.data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Record with ID '{id_fila}' not found in table '{tabla}'."
                )
            
            # Return the content or empty string if NULL
            content = response.data.get(columna)
            return content if content is not None else ""
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error fetching record content: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error while fetching record: {str(e)}"
            )
    
    async def update_record_content(self, tabla: str, columna: str, id_fila: str, new_content: str) -> bool:
        """
        Update the content of the specified field.
        
        Args:
            tabla: Table name
            columna: Column name
            id_fila: Record ID (UUID)
            new_content: New content to set
            
        Returns:
            True if update was successful
            
        Raises:
            HTTPException: If update fails
        """
        supabase = await self._get_client()
        
        try:
            response = supabase.table(tabla).update({columna: new_content}).eq('id', id_fila).execute()
            
            if not response.data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Record with ID '{id_fila}' not found in table '{tabla}' for update."
                )
            
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating record content: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error while updating record: {str(e)}"
            )
    
    def apply_reemplazar_fragmento(self, content: str, instruction: InstruccionCambio) -> Tuple[str, bool]:
        """Apply replace fragment instruction."""
        if not instruction.texto_a_buscar or instruction.nuevo_texto is None:
            logger.warning("Missing required fields for reemplazar_fragmento instruction")
            return content, False
        
        if instruction.texto_a_buscar not in content:
            logger.debug(f"Text to replace not found: '{instruction.texto_a_buscar}'")
            return content, False
        
        if instruction.reemplazar_todas_las_ocurrencias:
            new_content = content.replace(instruction.texto_a_buscar, instruction.nuevo_texto)
        else:
            new_content = content.replace(instruction.texto_a_buscar, instruction.nuevo_texto, 1)
        
        changed = new_content != content
        logger.debug(f"Replace operation: changed={changed}")
        return new_content, changed
    
    def apply_anadir_texto(self, content: str, instruction: InstruccionCambio) -> Tuple[str, bool]:
        """Apply add text instruction."""
        if not instruction.posicion or not instruction.texto_a_anadir:
            logger.warning("Missing required fields for anadir_texto instruction")
            return content, False
        
        if instruction.posicion == "inicio_campo":
            new_content = instruction.texto_a_anadir + content
            return new_content, True
        
        elif instruction.posicion == "final_campo":
            new_content = content + instruction.texto_a_anadir
            return new_content, True
        
        elif instruction.posicion in ["despues_de_marcador", "antes_de_marcador"]:
            if not instruction.texto_marcador_referencia:
                logger.warning("Missing texto_marcador_referencia for marker-based position")
                return content, False
            
            marker_pos = content.find(instruction.texto_marcador_referencia)
            
            if marker_pos == -1:
                # Marker not found
                if instruction.crear_marcador_si_no_existe:
                    # Add marker at the end, then add text
                    if instruction.posicion == "despues_de_marcador":
                        new_content = content + instruction.texto_marcador_referencia + instruction.texto_a_anadir
                    else:  # antes_de_marcador
                        new_content = content + instruction.texto_a_anadir + instruction.texto_marcador_referencia
                    return new_content, True
                else:
                    logger.debug(f"Marker not found and crear_marcador_si_no_existe=False: '{instruction.texto_marcador_referencia}'")
                    return content, False
            
            # Marker found, insert text
            if instruction.posicion == "despues_de_marcador":
                insert_pos = marker_pos + len(instruction.texto_marcador_referencia)
                new_content = content[:insert_pos] + instruction.texto_a_anadir + content[insert_pos:]
            else:  # antes_de_marcador
                new_content = content[:marker_pos] + instruction.texto_a_anadir + content[marker_pos:]
            
            return new_content, True
        
        logger.warning(f"Unknown position: {instruction.posicion}")
        return content, False

    def apply_eliminar_fragmento(self, content: str, instruction: InstruccionCambio) -> Tuple[str, bool]:
        """Apply delete fragment instruction."""
        if not instruction.texto_a_eliminar:
            logger.warning("Missing required field texto_a_eliminar for eliminar_fragmento instruction")
            return content, False

        if instruction.texto_a_eliminar not in content:
            logger.debug(f"Text to delete not found: '{instruction.texto_a_eliminar}'")
            return content, False

        if instruction.eliminar_todas_las_ocurrencias:
            new_content = content.replace(instruction.texto_a_eliminar, "")
        else:
            new_content = content.replace(instruction.texto_a_eliminar, "", 1)

        changed = new_content != content
        logger.debug(f"Delete operation: changed={changed}")
        return new_content, changed

    def apply_eliminar_seccion_delimitada(self, content: str, instruction: InstruccionCambio) -> Tuple[str, bool]:
        """Apply delete delimited section instruction."""
        if not instruction.marcador_inicio_seccion or not instruction.marcador_fin_seccion:
            logger.warning("Missing required markers for eliminar_seccion_delimitada instruction")
            return content, False

        start_marker = instruction.marcador_inicio_seccion
        end_marker = instruction.marcador_fin_seccion

        start_pos = content.find(start_marker)
        if start_pos == -1:
            logger.debug(f"Start marker not found: '{start_marker}'")
            return content, False

        # Find end marker after start marker
        end_pos = content.find(end_marker, start_pos + len(start_marker))
        if end_pos == -1:
            logger.debug(f"End marker not found after start marker: '{end_marker}'")
            return content, False

        # Delete from start of start_marker to end of end_marker
        end_pos_complete = end_pos + len(end_marker)
        new_content = content[:start_pos] + content[end_pos_complete:]

        changed = new_content != content
        logger.debug(f"Delete section operation: changed={changed}")
        return new_content, changed

    def apply_instruction(self, content: str, instruction: InstruccionCambio) -> Tuple[str, bool]:
        """
        Apply a single instruction to the content.

        Args:
            content: Current content
            instruction: Instruction to apply

        Returns:
            Tuple of (new_content, was_changed)
        """
        logger.debug(f"Applying instruction: {instruction.accion}")

        if instruction.accion == "reemplazar_fragmento":
            return self.apply_reemplazar_fragmento(content, instruction)
        elif instruction.accion == "anadir_texto":
            return self.apply_anadir_texto(content, instruction)
        elif instruction.accion == "eliminar_fragmento":
            return self.apply_eliminar_fragmento(content, instruction)
        elif instruction.accion == "eliminar_seccion_delimitada":
            return self.apply_eliminar_seccion_delimitada(content, instruction)
        else:
            logger.warning(f"Unknown instruction action: {instruction.accion}")
            return content, False

    async def process_text_modification(self, request: TextModificationRequest) -> TextModificationResponse:
        """
        Process a complete text modification request.

        Args:
            request: Text modification request

        Returns:
            Text modification response
        """
        logger.info(f"Processing text modification for {request.tabla}.{request.columna}, record {request.id_fila}")

        try:
            # Step 1: Validate table and column
            await self.validate_table_and_column(request.tabla, request.columna)

            # Step 2: Get current content
            original_content = await self.get_record_content(request.tabla, request.columna, request.id_fila)
            logger.debug(f"Original content length: {len(original_content)}")

            # Step 3: Apply instructions sequentially
            current_content = original_content
            instructions_processed = 0
            instructions_successful = 0
            failed_instruction_details = []

            for i, instruction in enumerate(request.instrucciones_de_cambio):
                instructions_processed += 1
                try:
                    new_content, was_changed = self.apply_instruction(current_content, instruction)
                    if was_changed:
                        current_content = new_content
                        instructions_successful += 1
                        logger.debug(f"Instruction {i+1} applied successfully")
                    else:
                        logger.debug(f"Instruction {i+1} did not change content")
                        failed_instruction_details.append(f"Instruction {i+1} ({instruction.accion}) did not apply")
                except Exception as e:
                    logger.error(f"Error applying instruction {i+1}: {e}")
                    failed_instruction_details.append(f"Instruction {i+1} failed: {str(e)}")

            # Step 4: Update database if content changed
            content_changed = current_content != original_content
            if content_changed:
                await self.update_record_content(request.tabla, request.columna, request.id_fila, current_content)
                logger.info(f"Content updated successfully. Length: {len(original_content)} -> {len(current_content)}")

            # Step 5: Generate response
            if instructions_successful == instructions_processed and content_changed:
                status = "success"
                message = f"El campo '{request.columna}' de la tabla '{request.tabla}' para el registro '{request.id_fila}' ha sido modificado exitosamente aplicando {instructions_successful} instrucciones."
            elif instructions_successful > 0 and content_changed:
                status = "partial_success"
                details = "; ".join(failed_instruction_details[:3])  # Limit details
                message = f"Se procesaron {instructions_processed} instrucciones. {instructions_successful} aplicadas. Detalles: {details}"
            elif instructions_successful == 0:
                status = "no_change_applied"
                message = f"Se procesaron {instructions_processed} instrucciones pero ninguna resultó en cambios al contenido."
            else:
                status = "no_change_applied"
                message = f"Las instrucciones se procesaron pero no resultaron en cambios al contenido del campo."

            return TextModificationResponse(
                status=status,
                message=message,
                cambios_realizados_en_bd=content_changed,
                instrucciones_procesadas=instructions_processed,
                instrucciones_exitosas=instructions_successful
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Unexpected error in text modification: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An unexpected error occurred during text modification: {str(e)}"
            )

# Create service instance
text_modification_service = TextModificationService()
