import React, { useState, useEffect } from 'react';
import Modal from '../UI/Modal';
import AsyncSelect from 'react-select/async';
import { StylesConfig, GroupBase, OptionsOrGroups, MultiValue, CSSObjectWithLabel } from 'react-select'; // Added MultiValue and CSSObjectWithLabel
import { useAuth } from '../../hooks/useAuth';
import type { AssociatedEntity } from '../../types/meeting.types';

// Define the option type for react-select
export interface SelectOptionType { // Renamed from SearchableDropdownOption for clarity
  value: string;
  label: string;
  subLabel?: string; // Keep subLabel if you want to render it customly or ensure it's part of label
}

type EntityType = 'empresa' | 'persona';

interface SelectExistingEntityModalProps {
  isOpen: boolean;
  onClose: () => void;
  entityType: EntityType | null;
  onEntitiesSelected: (entities: AssociatedEntity[]) => void; // Changed prop name and type
  // To prevent re-selecting already associated entities if needed, though SearchableDropdown doesn't directly support disabling specific options yet.
  // We can filter them out from the fetched list.
  alreadyAssociatedIds?: string[];
}

const SelectExistingEntityModal: React.FC<SelectExistingEntityModalProps> = ({
  isOpen,
  onClose,
  entityType,
  onEntitiesSelected, // Changed prop name
  alreadyAssociatedIds = [],
}) => {
  const { session } = useAuth();
  // options state is not strictly needed if AsyncSelect handles loading via loadOptions prop directly
  // const [options, setOptions] = useState<SelectOptionType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedEntities, setSelectedEntities] = useState<SelectOptionType[]>([]);
  const [isMenuInitiallyOpen, setIsMenuInitiallyOpen] = useState(true); // Control menu visibility

  const entityConfig = {
    empresa: {
      title: "Seleccionar Empresa Existente",
      placeholder: "Buscar empresa por nombre o NIF/CIF...",
      endpoint: "empresas",
      fetcher: async (searchTerm: string, token?: string | null) => {
        const endpoint = `${import.meta.env.VITE_API_BASE_URL}/empresas?search=${encodeURIComponent(searchTerm)}`;
        const response = await fetch(endpoint, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        if (!response.ok) throw new Error('Failed to fetch empresas');
        const data: { id: string, nombre: string, nif_cif?: string }[] = await response.json();
        return data
          .filter(e => !alreadyAssociatedIds.includes(e.id))
          .map(e => ({ value: e.id, label: e.nombre, subLabel: e.nif_cif ? `NIF/CIF: ${e.nif_cif}` : undefined } as SelectOptionType)); // Cast to SelectOptionType
      }
    },
    persona: {
      title: "Seleccionar Persona Existente",
      placeholder: "Buscar persona por nombre, apellidos, email...",
      endpoint: "personas",
      fetcher: async (searchTerm: string, token?: string | null): Promise<SelectOptionType[]> => { // Return Promise<SelectOptionType[]>
        const endpoint = `${import.meta.env.VITE_API_BASE_URL}/personas?search=${encodeURIComponent(searchTerm)}`;
        const response = await fetch(endpoint, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        if (!response.ok) throw new Error('Failed to fetch personas');
        const data: { id: string, nombre: string, apellidos: string, email?: string, cargo?: string, empresa?: { nombre: string } }[] = await response.json();
        return data
          .filter(p => !alreadyAssociatedIds.includes(p.id))
          .map(p => ({
            value: p.id,
            label: `${p.nombre} ${p.apellidos}`,
            subLabel: `${p.cargo || ''} en ${p.empresa?.nombre || 'N/A'}`.trim()
          } as SelectOptionType)); // Cast to SelectOptionType
      }
    },

  };

  const currentConfig = entityType ? entityConfig[entityType] : null;

  // loadOptions for AsyncSelect
  const promiseOptions = (inputValue: string): Promise<OptionsOrGroups<SelectOptionType, GroupBase<SelectOptionType>>> => {
    return new Promise((resolve) => {
      if (!currentConfig || !session?.access_token) {
        resolve([]);
        return;
      }
      setIsLoading(true);
      // Use an async IIFE to handle the await currentConfig.fetcher
      (async () => {
        try {
          const fetchedOptions = await currentConfig.fetcher(inputValue, session.access_token);
          resolve(fetchedOptions);
        } catch (error) {
          console.error(`Error fetching ${entityType}:`, error);
          resolve([]);
        } finally {
          setIsLoading(false);
        }
      })();
    });
  };

  useEffect(() => {
    if (isOpen && entityType) {
      setSelectedEntities([]); // Reset selection
      setIsMenuInitiallyOpen(true); // Open menu when modal becomes visible for an entity
      // AsyncSelect with defaultOptions should load initial options automatically
    } else if (!isOpen) {
      setIsMenuInitiallyOpen(false); // Ensure menu is marked as closed when modal is not open
    }
  }, [isOpen, entityType]);


  const handleSelect = () => {
    if (selectedEntities.length > 0 && entityType) {
      const entitiesToPass: AssociatedEntity[] = selectedEntities.map(opt => ({
        id: opt.value,
        nombre: opt.label,
        tipo: entityType as EntityType, // Ensure entityType is not null here
      }));
      onEntitiesSelected(entitiesToPass);
      onClose();
    }
  };

  if (!isOpen || !currentConfig) return null;

  // Custom styles for react-select
  const selectStyles: StylesConfig<SelectOptionType, true, GroupBase<SelectOptionType>> = {
    control: (provided: CSSObjectWithLabel, state) => ({
      ...provided,
      minHeight: state.hasValue ? '60px' : '42px', // Adjust height based on whether items are selected
      // If you want the input bar itself to be taller even when empty:
      // minHeight: '60px',
    }),
    valueContainer: (provided: CSSObjectWithLabel) => ({
      ...provided,
      padding: '2px 8px',
      maxHeight: '150px', // Max height for the area showing selected tags before it scrolls
      overflowY: 'auto',
    }),
    menu: (provided: CSSObjectWithLabel) => ({ // Style for the overall menu container
        ...provided,
        // position: 'static', // This would make it part of the normal flow, but might break default positioning
        // Instead, we ensure the modal is tall enough and menuList has good height.
    }),
    menuList: (provided: CSSObjectWithLabel) => ({
      ...provided,
      maxHeight: '350px', // Significant height for the dropdown list of options
      // Forcing it to be "open" visually by default is tricky without `menuIsOpen`
      // and ensuring the modal content area can show it.
    }),
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={currentConfig.title} size="xl">
      {/* Make the main content div a flex column to control layout better */}
      <div className="flex flex-col space-y-4" style={{ minHeight: '450px', maxHeight: '70vh', overflowY: 'hidden' /* Prevent double scrollbars, modal handles main scroll */ }}>
        {/* Wrapper for AsyncSelect to control its growth and menu behavior if needed */}
        <div className="flex-grow" style={{ minHeight: '350px' /* Give substantial space for select + open menu */ }}>
          <AsyncSelect
            isMulti
            cacheOptions
          defaultOptions // Loads initial set of options on mount (can be true or an array)
          loadOptions={promiseOptions}
          value={selectedEntities}
          onChange={(selectedOpts: MultiValue<SelectOptionType>) => {
            setSelectedEntities(selectedOpts as SelectOptionType[]);
            // setIsMenuInitiallyOpen(false); // Optionally close menu after selection
          }}
          placeholder={currentConfig.placeholder}
          isLoading={isLoading}
          styles={selectStyles}
          menuIsOpen={isMenuInitiallyOpen || undefined} // Control menu state; undefined lets react-select manage it after initial open
          onFocus={() => setIsMenuInitiallyOpen(true)} // Re-open if user focuses
          // onBlur={() => setIsMenuInitiallyOpen(false)} // Optionally close on blur, might be too aggressive
          noOptionsMessage={() => 'No hay opciones'}
          loadingMessage={() => 'Cargando...'}
          className="text-sm"
          classNamePrefix="react-select" // For more specific global styling if needed
        />
        </div> {/* This was the missing closing tag for the flex-grow div */}
        {/* Re-adding the action buttons */}
        <div className="flex justify-end space-x-2 pt-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancelar
          </button>
          <button
            type="button"
            onClick={handleSelect}
            disabled={selectedEntities.length === 0 || isLoading} // Disable if no entities selected
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            Seleccionar
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default SelectExistingEntityModal;
