import logging
from uuid import UUID
from fastapi import <PERSON>TTPException, status
from typing import List, Optional

from app.core.database import get_supabase_client
from app.models.agent import Agent, AgentDetails
from app.models.tool import Tool

logger = logging.getLogger(__name__)

async def get_agent_details(agent_id: UUID) -> AgentDetails:
    """
    Fetches detailed information for a specific agent, including associated
    tools and companions, from the database.

    Args:
        agent_id: The UUID of the agent to fetch.

    Returns:
        An AgentDetails object containing the agent's data, tools, and companions.

    Raises:
        HTTPException: If the agent is not found (404) or if a database error occurs (500).
    """
    try:
        supabase = get_supabase_client()
        logger.info(f"Fetching details for agent_id: {agent_id}")

        # Step 1: Fetch agent and associated tools
        logger.info("Fetching agent and tools...")
        agent_tools_query = supabase.table("agentes").select(
            """
            *,
            agentes_tools (
                tools (*)
            )
            """
        ).eq("id", agent_id).maybe_single()
        response_agent_tools = agent_tools_query.execute() # Synchronous execute
        agent_data = response_agent_tools.data

        if not agent_data:
            logger.warning(f"Agent with id {agent_id} not found.")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent with id {agent_id} not found",
            )

        logger.debug(f"Raw agent data received: {agent_data}")

        # Extract tools
        tools_list: List[Tool] = []
        if agent_data.get("agentes_tools"):
            for tool_link in agent_data["agentes_tools"]:
                if tool_link.get("tools"):
                    try:
                        tools_list.append(Tool.model_validate(tool_link["tools"]))
                    except Exception as e:
                        logger.warning(f"Failed to validate tool data: {tool_link.get('tools')}. Error: {e}")


        # Step 2: Fetch companion IDs
        logger.info("Fetching companion IDs...")
        companion_ids_response = supabase.table("agentes_companeros").select("companero_id").eq("agente_id", agent_id).execute() # Synchronous execute
        companion_ids = [item['companero_id'] for item in companion_ids_response.data] if companion_ids_response.data else []

        # Step 3: Fetch companion details if IDs exist
        companions_list: List[Agent] = []
        if companion_ids:
            logger.info(f"Fetching details for {len(companion_ids)} companions...")
            companions_response = supabase.table("agentes").select("*").in_("id", companion_ids).execute() # Synchronous execute
            if companions_response.data:
                 for comp_data in companions_response.data:
                     try:
                         companions_list.append(Agent.model_validate(comp_data))
                     except Exception as e:
                         logger.warning(f"Failed to validate companion data: {comp_data}. Error: {e}")
            else:
                 logger.warning(f"Could not fetch details for companion IDs: {companion_ids}")
        else:
            logger.info("Agent has no companions.")


        # Create the final AgentDetails object
        # We need to remove the relationship fields before validating the base Agent part
        base_agent_data = {k: v for k, v in agent_data.items() if k not in ["agentes_tools"]} # Only remove tools now

        try:
            agent_details = AgentDetails(
                **base_agent_data,
                tools=tools_list,
                companeros=companions_list
            )
        except Exception as e:
             logger.error(f"Failed to create AgentDetails object for agent {agent_id}. Error: {e}")
             logger.debug(f"Base data: {base_agent_data}")
             logger.debug(f"Tools: {tools_list}")
             logger.debug(f"Companions: {companions_list}")
             raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process agent details."
             )


        logger.info(f"Successfully fetched details for agent_id: {agent_id}")
        return agent_details

    except HTTPException as http_exc:
        # Re-raise HTTPExceptions directly
        raise http_exc
    except Exception as e:
        logger.exception(f"Database error while fetching agent details for {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while fetching agent details: {e}",
        )

# Placeholder for get_active_agents function (needed for AgentSelector later)
async def get_active_agents() -> List[Agent]:
    """
    Fetches all active agents from the database.
    (Placeholder - implementation needed for AgentSelector)
    """
    try:
        supabase = get_supabase_client()
        logger.info("Fetching active agents")
        # Execute the query (execute() is likely synchronous now)
        response = supabase.table("agentes").select("*").eq("activo", True).execute()
        if response.data:
             # Validate data against the Agent model
             validated_agents = [Agent.model_validate(agent_data) for agent_data in response.data]
             logger.info(f"Found {len(validated_agents)} active agents.")
             return validated_agents
        else:
            logger.info("No active agents found.")
            return []
    except Exception as e:
        logger.exception(f"Database error while fetching active agents: {e}")
        # Depending on use case, might return empty list or raise HTTPException
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred while fetching active agents: {e}",
        )