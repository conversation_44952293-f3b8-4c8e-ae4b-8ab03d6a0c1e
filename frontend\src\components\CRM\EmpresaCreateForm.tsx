import React from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// Define Zod schema for validation based on Pydantic model EmpresaCreate
// and form fields from docs/FEATURES/CRM_ENTITY_QUICK_ADD_FORMS.md
const empresaSchema = z.object({
  nombre: z.string().min(1, "Nombre es requerido"),
  nif_cif: z.string().optional(),
  sector: z.string().optional(),
  descripcion: z.string().optional(),
  logo_url: z.string().url("Debe ser una URL válida").optional().or(z.literal('')),
  direccion: z.string().optional(),
  direccion_fiscal: z.string().optional(),
  telefono: z.string().optional(),
  email_principal: z.string().email("Debe ser un email válido").optional().or(z.literal('')),
  website: z.string().url("Debe ser una URL válida").optional().or(z.literal('')),
  tipo_empresa: z.string().optional(),
  tipo_relacion: z.enum(['Cliente', 'Colaborador', 'Otro', 'Lead'], { errorMap: () => ({ message: "Tipo de relación es requerido" }) }),
  activo: z.boolean(), // Removed .default(true) here, will rely on RHF defaultValues
  info_adicional: z.string().optional(),
});

type EmpresaFormValues = z.infer<typeof empresaSchema>;

// This type should ideally match the backend's Empresa response model
export interface EmpresaApiResponse extends EmpresaFormValues {
  id: string; // UUIDs are strings in JS/TS
  fecha_alta?: string; // Date as string
  created_at: string; // DateTime as string
  updated_at: string; // DateTime as string
}

interface EmpresaCreateFormProps {
  onSubmitSuccess: (data: EmpresaApiResponse) => void; // Callback on successful submission
  onCancel: () => void; // Callback for cancelling the form
}

const EmpresaCreateForm: React.FC<EmpresaCreateFormProps> = ({ onSubmitSuccess, onCancel }) => {
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<EmpresaFormValues>({
    resolver: zodResolver(empresaSchema),
    defaultValues: {
      nombre: '',
      nif_cif: '',
      sector: '',
      descripcion: '',
      logo_url: '',
      direccion: '',
      direccion_fiscal: '',
      telefono: '',
      email_principal: '',
      website: '',
      tipo_empresa: '',
      tipo_relacion: undefined, // Or an initial valid enum value like 'Cliente'
      activo: true, // Matches schema default
      info_adicional: '',
    }
  });

  const onSubmit: SubmitHandler<EmpresaFormValues> = async (data) => {
    try {
      // Replace with actual API call
      console.log('Submitting Empresa Data:', data);
      // const response = await fetch('/api/v1/empresas', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(data),
      // });
      // if (!response.ok) {
      //   const errorData = await response.json();
      //   throw new Error(errorData.detail || 'Error al crear empresa');
      // }
      // const result = await response.json();
      // onSubmitSuccess(result as EmpresaApiResponse); // Cast if API call is made
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      // Simulate a successful API response structure
      const mockApiResponse: EmpresaApiResponse = {
        ...data,
        id: crypto.randomUUID(), // Generate a mock UUID
        fecha_alta: new Date().toISOString().split('T')[0], // Simulate date
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      onSubmitSuccess(mockApiResponse); // Use the mock API response
    } catch (error) {
      console.error('Error submitting empresa form:', error);
      // Handle error display to user, e.g., using a toast notification
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const formFieldClass = "mb-4";
  const labelClass = "block text-sm font-medium text-gray-700 mb-1";
  const inputClass = "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm";
  const errorClass = "mt-1 text-xs text-red-600";

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-4 bg-white shadow-md rounded-lg">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Crear Nueva Empresa</h2>

      <div className={formFieldClass}>
        <label htmlFor="nombre" className={labelClass}>Nombre*</label>
        <input id="nombre" {...register("nombre")} className={inputClass} />
        {errors.nombre && <p className={errorClass}>{errors.nombre.message}</p>}
      </div>

      <div className={formFieldClass}>
        <label htmlFor="tipo_relacion" className={labelClass}>Tipo de Relación*</label>
        <select id="tipo_relacion" {...register("tipo_relacion")} className={inputClass}>
          <option value="">Seleccione un tipo</option>
          <option value="Cliente">Cliente</option>
          <option value="Colaborador">Colaborador</option>
          <option value="Lead">Lead</option>
          <option value="Otro">Otro</option>
        </select>
        {errors.tipo_relacion && <p className={errorClass}>{errors.tipo_relacion.message}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={formFieldClass}>
          <label htmlFor="nif_cif" className={labelClass}>NIF/CIF</label>
          <input id="nif_cif" {...register("nif_cif")} className={inputClass} />
          {errors.nif_cif && <p className={errorClass}>{errors.nif_cif.message}</p>}
        </div>

        <div className={formFieldClass}>
          <label htmlFor="sector" className={labelClass}>Sector</label>
          <input id="sector" {...register("sector")} className={inputClass} />
          {errors.sector && <p className={errorClass}>{errors.sector.message}</p>}
        </div>
      </div>

      <div className={formFieldClass}>
        <label htmlFor="descripcion" className={labelClass}>Descripción</label>
        <textarea id="descripcion" {...register("descripcion")} rows={3} className={inputClass}></textarea>
        {errors.descripcion && <p className={errorClass}>{errors.descripcion.message}</p>}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={formFieldClass}>
          <label htmlFor="logo_url" className={labelClass}>Logo URL</label>
          <input id="logo_url" type="url" {...register("logo_url")} className={inputClass} placeholder="https://example.com/logo.png"/>
          {errors.logo_url && <p className={errorClass}>{errors.logo_url.message}</p>}
        </div>

        <div className={formFieldClass}>
          <label htmlFor="website" className={labelClass}>Website</label>
          <input id="website" type="url" {...register("website")} className={inputClass} placeholder="https://example.com"/>
          {errors.website && <p className={errorClass}>{errors.website.message}</p>}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={formFieldClass}>
          <label htmlFor="direccion" className={labelClass}>Dirección</label>
          <input id="direccion" {...register("direccion")} className={inputClass} />
          {errors.direccion && <p className={errorClass}>{errors.direccion.message}</p>}
        </div>

        <div className={formFieldClass}>
          <label htmlFor="direccion_fiscal" className={labelClass}>Dirección Fiscal</label>
          <input id="direccion_fiscal" {...register("direccion_fiscal")} className={inputClass} />
          {errors.direccion_fiscal && <p className={errorClass}>{errors.direccion_fiscal.message}</p>}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={formFieldClass}>
          <label htmlFor="telefono" className={labelClass}>Teléfono</label>
          <input id="telefono" {...register("telefono")} className={inputClass} />
          {errors.telefono && <p className={errorClass}>{errors.telefono.message}</p>}
        </div>

        <div className={formFieldClass}>
          <label htmlFor="email_principal" className={labelClass}>Email Principal</label>
          <input id="email_principal" type="email" {...register("email_principal")} className={inputClass} placeholder="<EMAIL>"/>
          {errors.email_principal && <p className={errorClass}>{errors.email_principal.message}</p>}
        </div>
      </div>
      
      <div className={formFieldClass}>
        <label htmlFor="tipo_empresa" className={labelClass}>Tipo Empresa</label>
        <input id="tipo_empresa" {...register("tipo_empresa")} className={inputClass} />
        {errors.tipo_empresa && <p className={errorClass}>{errors.tipo_empresa.message}</p>}
      </div>

      <div className={formFieldClass}>
        <label htmlFor="info_adicional" className={labelClass}>Info Adicional</label>
        <textarea id="info_adicional" {...register("info_adicional")} rows={3} className={inputClass}></textarea>
        {errors.info_adicional && <p className={errorClass}>{errors.info_adicional.message}</p>}
      </div>

      <div className={`${formFieldClass} flex items-center`}>
        <input id="activo" type="checkbox" {...register("activo")} className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" />
        <label htmlFor="activo" className="ml-2 block text-sm text-gray-900">Activo</label>
        {errors.activo && <p className={`${errorClass} ml-2`}>{errors.activo.message}</p>}
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button type="button" onClick={onCancel} disabled={isSubmitting} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
          Cancelar
        </button>
        <button type="submit" disabled={isSubmitting} className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
          {isSubmitting ? 'Creando...' : 'Crear Empresa'}
        </button>
      </div>
    </form>
  );
};

export default EmpresaCreateForm;