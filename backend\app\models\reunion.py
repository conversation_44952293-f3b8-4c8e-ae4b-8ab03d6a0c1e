from pydantic import BaseModel, HttpUrl, EmailStr, field_validator
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
import datetime
import json

# Import CRM models
from .crm_empresa import Empresa
from .crm_persona import Persona

# User model for uploader information
from .user import User # Assuming User model has id, nombre, email

# --- Base and Create Models for Main Reunion Record ---
class ReunionBase(BaseModel):
    titulo: Optional[str] = None
    observaciones_iniciales: Optional[str] = None
    fecha_reunion: Optional[datetime.datetime] = None
    info_adicional: Optional[str] = None
    entrevista: Optional[bool] = None
    video: Optional[bool] = None

class ReunionCreate(ReunionBase):
    empresas_asociadas_ids: Optional[List[UUID]] = []
    personas_asociadas_ids: Optional[List[UUID]] = []


    url_grabacion_publica: HttpUrl
    file_storage_path: str

# Simplified model for the uploader's basic info relevant to speaker assignment
class ReunionUploaderInfo(BaseModel):
    id: UUID
    nombre: Optional[str] = None
    email: EmailStr # Assuming email is always present for a user

    class Config:
        from_attributes = True

class Reunion(ReunionBase):
    id: UUID
    user_id: UUID # Still keep the ID for direct reference to the user table

    # Populated by the service layer when fetching a reunion
    uploader_info: Optional[ReunionUploaderInfo] = None

    url_grabacion_original: Optional[str] = None
    url_grabacion_publica: Optional[HttpUrl] = None
    transcripcion_raw: Optional[str] = None
    transcripcion_final: Optional[str] = None
    resumen: Optional[str] = None
    puntos_clave: Optional[Union[Dict[str, Any], str]] = None
    preguntas_pendientes: Optional[Union[List[Dict[str, Any]], str]] = None
    estado_procesamiento: Optional[str] = None
    created_at: datetime.datetime
    updated_at: datetime.datetime

    # NEW: Fields to hold lists of fully populated associated CRM entities
    # Initialized to empty lists to ensure they are always present in the response
    empresas_asociadas: List[Empresa] = []
    personas_asociadas: List[Persona] = []


    @field_validator('puntos_clave', mode='before')
    @classmethod
    def validate_puntos_clave(cls, v):
        """Convert puntos_clave to dict format for backward compatibility."""
        if v is None:
            return None

        # Handle list/array case (most common from n8n AI processing)
        if isinstance(v, list):
            return {"puntos": v}

        # Handle dict case (already in correct format)
        if isinstance(v, dict):
            return v

        # Handle string case
        if isinstance(v, str):
            # Try to parse as JSON first
            try:
                parsed = json.loads(v)
                if isinstance(parsed, list):
                    return {"puntos": parsed}
                elif isinstance(parsed, dict):
                    return parsed
                else:
                    # If it's a simple string value, wrap it
                    return {"contenido": v}
            except (json.JSONDecodeError, TypeError):
                # If parsing fails, wrap in a dict
                return {"contenido": v}

        # For any other type, try to convert to string and wrap
        return {"contenido": str(v)}

    class Config:
        from_attributes = True

# --- Models for Speaker Assignments ---
class SpeakerAssignmentBase(BaseModel):
    speaker_tag: str # e.g., "Speaker 0", "Speaker 1"
    # "persona", "lead_contacto", "usuario" (for the uploader)
    asignado_a_tipo: str
    asignado_a_id: UUID
    nombre_asignado: str # Denormalized name for easier display

class SpeakerAssignmentCreate(SpeakerAssignmentBase):
    pass

class SpeakerAssignment(SpeakerAssignmentBase):
    id: UUID
    reunion_id: UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        from_attributes = True
