-- Migration: Add 'Lead' to tipo_relacion enum
-- Date: 2025-01-25
-- Description: Add 'Lead' enum value to the tipo_relacion_enum type to support lead companies

-- First, check current enum values (for reference)
-- SELECT t.typname, e.enumlabel 
-- FROM pg_type t 
-- JOIN pg_enum e ON t.oid = e.enumtypid 
-- WHERE t.typname LIKE '%tipo%' OR t.typname LIKE '%relacion%'
-- ORDER BY t.typname, e.enumsortorder;

-- Add 'Lead' to the tipo_relacion enum if it doesn't already exist
-- Note: PostgreSQL doesn't have IF NOT EXISTS for ALTER TYPE ADD VALUE
-- So we need to check if the value already exists first

DO $$
BEGIN
    -- Check if 'Lead' already exists in the enum
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_enum e
        JOIN pg_type t ON e.enumtypid = t.oid
        WHERE t.typname = 'tipo_relacion_enum' 
        AND e.enumlabel = 'Lead'
    ) THEN
        -- Add 'Lead' to the enum
        ALTER TYPE tipo_relacion_enum ADD VALUE 'Lead';
        RAISE NOTICE 'Added ''Lead'' to tipo_relacion_enum';
    ELSE
        RAISE NOTICE '''Lead'' already exists in tipo_relacion_enum';
    END IF;
END $$;

-- Verify the enum values after the change
SELECT t.typname as enum_name, e.enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid 
WHERE t.typname = 'tipo_relacion_enum'
ORDER BY e.enumsortorder;
