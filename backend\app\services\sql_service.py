import re
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from supabase import Client as SupabaseClient, PostgrestAPIResponse # Import response type
from postgrest import APIError # Import specific error for better handling
from typing import List, Dict, Any, Tuple, Optional # Added Optional

from ..core.database import get_supabase_client # Function to get the Supabase client instance
from ..core.config import settings

# Pre-compile regex patterns for efficiency
FORBIDDEN_PATTERNS = [
    re.compile(r"(?i)\bDROP\s+TABLE\b"),
    re.compile(r"(?i)\bALTER\s+TABLE\b.*\bDROP\s+COLUMN\b"),
    re.compile(r"(?i)^\s*DELETE\s+FROM\b") # Added pattern to block DELETE (anchored to start)
]

async def validate_sql_command(sql: str) -> None:
    """
    Checks if the SQL command contains forbidden patterns.

    Args:
        sql: The SQL command string.

    Raises:
        HTTPException 400: If a forbidden pattern is found.
    """
    for pattern in FORBIDDEN_PATTERNS:
        if pattern.search(sql):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="SQL command rejected due to security policy (contains forbidden patterns)."
            )

async def execute_sql_command(sql: str) -> Tuple[Any, Optional[int]]:
    """
    Validates and executes a raw SQL command using the Supabase client.

    Args:
        sql: The SQL command string.

    Returns:
        A tuple containing:
        - The query result (e.g., list of dicts for SELECT, None otherwise).
        - The number of rows affected (if applicable, otherwise None).

    Raises:
        HTTPException 400: If the SQL command is forbidden.
        HTTPException 500: If a database execution error occurs.
    """
    await validate_sql_command(sql) # First, validate the command

    supabase: SupabaseClient = get_supabase_client() # Removed await since client is synchronous
    result_data = None
    rows_affected = None

    try:
        # Strip trailing whitespace and semicolon before sending to DB function
        clean_sql = sql.strip().rstrip(';')

        # Call the database function using rpc with the cleaned SQL
        # Removed PostgrestAPIResponse type hint to debug TypeError
        # Removed await from .execute() as it seems non-awaitable here
        response = supabase.rpc(
            'execute_dynamic_sql',
            {'sql_command': clean_sql} # Use cleaned SQL
        ).execute() # Execute synchronously after building the RPC call

        # The database function returns a single JSON object like:
        # {"status": "success", "result": [...], "rows_affected": N}
        # or raises an error which is caught below.

        if response.data:
            # Assuming the function always returns data on success
            response_payload = response.data
            result_data = response_payload.get('result') # This will be JSON array for SELECT, null otherwise
            rows_affected = response_payload.get('rows_affected') # Will be 0 for SELECT, count otherwise
        else:
             # Should not happen if the DB function works correctly, but handle defensively
             print(f"Warning: RPC call 'execute_dynamic_sql' returned no data for SQL: {sql}")
             result_data = None
             rows_affected = None

        return result_data, rows_affected

    except APIError as db_error: # Catch specific Supabase/Postgrest errors
        # The database function raises the original error message
        error_message = str(db_error) # Get the error message string
        # Log the database error
        print(f"Database error executing SQL: {error_message}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {error_message}" # Pass the specific error back
        )
    except Exception as e: # Catch broader errors during execution or response parsing
        # Log the unexpected error for debugging
        print(f"Unexpected error executing SQL via RPC: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred during SQL execution: {str(e)}"
        )